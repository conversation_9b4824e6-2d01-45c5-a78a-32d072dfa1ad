package me.socure.common.kyc.model.es.result

import me.socure.common.kyc.model.es.result.RecordsCleanupHelper._
import me.socure.common.kyc.util.SSNUtil

/**
 * DOB cleanup based on SSN issuance year range.
 * Filters DOBs when multiple are present based on SSN issuance year logic.
 *
 * DOB selection logic:
 * 1. Do filter if more than one DOB present.
 * 2. Select DOB in range of SSN issued (low date - high date + 1)
 *    if multiple SSNs present then take the lowest SSN date and highest SSN date of SSNs present.
 * 3. If all DOBs are prior to the SSN issuance year, select the DOB closest to the range. (low)
 * 4. If SSN low year = SSN high year then prefer +1 and -1 range.
 * 5. If all DOBs are after to the SSN issuance year, select the DOB closest to the range. (high)
 */
object DOBSSNIssuanceCleanup extends RecordsCleanupOperation {
  val name = "DOBSSNIssuanceCleanup"

  def apply(input: RecordCleanupOperationInput): RecordCleanupOperationResult = {
    if (input.records.dob.length > 1 && input.records.ssn.nonEmpty) {

      // Get SSN issuance year ranges from all SSNs
      val ssnIssuanceRanges = input.records.ssn.indices.map { index =>
        val ssn = getElement(input.records.ssn, index)
        val ssnIssuedLow = getElement(input.records.ssnYearLow, index).orElse(
          SSNUtil.ssnLookup(ssn, useV2 = true).map(_.ssnYearLow)
        )
        val ssnIssuedHigh = getElement(input.records.ssnYearHigh, index).orElse(
          SSNUtil.ssnLookup(ssn, useV2 = true).map(_.ssnYearHigh)
        )
        (ssnIssuedLow.flatMap(parseYear), ssnIssuedHigh.flatMap(parseYear))
      }.filter { case (low, high) => low.isDefined || high.isDefined }

      if (ssnIssuanceRanges.nonEmpty) {
        // Determine overall SSN issuance range
        val lowYears = ssnIssuanceRanges.flatMap(_._1)
        val highYears = ssnIssuanceRanges.flatMap(_._2)

        val overallLowYear = if (lowYears.nonEmpty) Some(lowYears.min) else None
        val overallHighYear = if (highYears.nonEmpty) Some(highYears.max) else None

        if (overallLowYear.isDefined || overallHighYear.isDefined) {
          val dobsWithYears = input.records.dob.zipWithIndex.flatMap { case (dob, index) =>
            parseYear(dob).map(year => (dob, year, index))
          }

          if (dobsWithYears.nonEmpty) {
            val inputDOB = input.request.dob.map(_.value)
            val selectedDOBs = selectDOBsBasedOnSSNRange(dobsWithYears, overallLowYear, overallHighYear, inputDOB)

            if (selectedDOBs.length < input.records.dob.length) {
              val selectedIndices = selectedDOBs.map(_._3).toSet
              val cleanedDobs = selectedDOBs.map(_._1)
              // Safely map selected indices to piiRowIDs, handling potential index out of bounds
              val cleanedRowIds = selectedIndices.toArray.sorted.filter(_ < input.records.piiRowIDs.dob.length)
                .map(input.records.piiRowIDs.dob(_))

              val cleanedRecords = input.records.copy(
                dob = cleanedDobs,
                piiRowIDs = input.records.piiRowIDs.copy(dob = cleanedRowIds)
              )

              val cleanedIdentityRecords = input.identityRecord.map { identityRecord =>
                val (accepted, removed) = identityRecord.dob.zipWithIndex.partition { case (_, index) =>
                  selectedIndices.contains(index)
                }
                identityRecord.copy(
                  dob = accepted.map(_._1),
                  removed = identityRecord.removed.append(IdentityRecordRemoved(dob = removed.map(_._1)))
                )
              }

              return RecordCleanupOperationResult(updated = true, cleanedRecords, cleanedIdentityRecords)
            }
          }
        }
      }
    }
    RecordCleanupOperationResult(updated = false, input.records, input.identityRecord)
  }

  private def selectDOBsBasedOnSSNRange(dobsWithYears: Array[(String, Int, Int)],
                                       lowYear: Option[Int],
                                       highYear: Option[Int],
                                       inputDOB: Option[String]): Array[(String, Int, Int)] = {
    val low = lowYear.getOrElse(Int.MinValue)
    val high = highYear.getOrElse(Int.MaxValue)

    // Always include DOBs that match the input DOB (don't filter them out)
    val inputDOBMatches = inputDOB match {
      case Some(inputDobValue) => dobsWithYears.filter { case (dob, _, _) => dob == inputDobValue }
      case None => Array.empty[(String, Int, Int)]
    }

    // Priority 1: DOBs within SSN issuance range (low to high + 1)
    val withinRange = dobsWithYears.filter { case (_, year, _) =>
      year >= low && year <= (high + 1)
    }

    if (withinRange.nonEmpty) {
      // Return within range DOBs, but always include input DOB matches even if outside range
      val combined = (withinRange ++ inputDOBMatches)
      // Remove duplicates by index and sort by original index to maintain order
      return combined.groupBy(_._3).map(_._2.head).toArray.sortBy(_._3)
    }

    // Check if SSN low year = high year, prefer ±1 range
    if (lowYear.isDefined && highYear.isDefined && lowYear.get == highYear.get) {
      val extendedRange = dobsWithYears.filter { case (_, year, _) =>
        year >= (low - 1) && year <= (high + 1)
      }
      if (extendedRange.nonEmpty) {
        val combined = (extendedRange ++ inputDOBMatches)
        return combined.groupBy(_._3).map(_._2.head).toArray.sortBy(_._3)
      }
    }

    // Priority 2: If all DOBs are prior to range, select closest to low year
    val priorToDOBs = dobsWithYears.filter { case (_, year, _) => year < low }
    if (priorToDOBs.length == dobsWithYears.length && priorToDOBs.nonEmpty) {
      val closestToLow = priorToDOBs.maxBy(_._2)
      val combined = (Array(closestToLow) ++ inputDOBMatches)
      return combined.groupBy(_._3).map(_._2.head).toArray.sortBy(_._3)
    }

    // Priority 3: If all DOBs are after range, select closest to high year
    val afterDOBs = dobsWithYears.filter { case (_, year, _) => year > (high + 1) }
    if (afterDOBs.length == dobsWithYears.length && afterDOBs.nonEmpty) {
      val closestToHigh = afterDOBs.minBy(_._2)
      val combined = (Array(closestToHigh) ++ inputDOBMatches)
      return combined.groupBy(_._3).map(_._2.head).toArray.sortBy(_._3)
    }

    // Priority 4: Mixed scenario - select closest DOBs from both sides
    if (priorToDOBs.nonEmpty && afterDOBs.nonEmpty) {
      val closestToLow = priorToDOBs.maxBy(_._2)
      val closestToHigh = afterDOBs.minBy(_._2)
      val selected = Array(closestToLow, closestToHigh)
      val combined = (selected ++ inputDOBMatches)
      return combined.groupBy(_._3).map(_._2.head).toArray.sortBy(_._3)
    }

    // Fallback: return input DOB matches if available, otherwise return all DOBs
    if (inputDOBMatches.nonEmpty) {
      inputDOBMatches
    } else {
      dobsWithYears // Return all DOBs as last resort
    }
  }
}
